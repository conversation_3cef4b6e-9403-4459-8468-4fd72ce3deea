import ast
import asyncio
import os
import sys
import logging
import time
import traceback
from ctypes import c_ubyte
from datetime import datetime

import cv2
import numpy as np
import requests
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QImage, QPixmap
from PyQt5.QtWidgets import QFrame, QHBoxLayout, QApplication, QVBoxLayout, QDialog
from _ctypes import addressof
from qfluentwidgets import FluentWindow, SubtitleLabel, ComboBox, ImageLabel, \
    GroupHeaderCardWidget, BodyLabel, PrimaryPushButton, DoubleSpinBox, InfoBar, InfoBarPosition, \
    MessageBoxBase, PasswordLineEdit
from qfluentwidgets import FluentIcon as FIF

import gxipy as gx
from pymodbus.client import AsyncModbusTcpClient



logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='detection_app.log',
    filemode='a'
)
logger = logging.getLogger('detection_app')

class FullscreenImageViewer(QDialog):
    def __init__(self, pixmap):
        super().__init__()
        self.setWindowFlags(Qt.Window)
        self.setWindowTitle("全屏预览")
        self.setWindowState(Qt.WindowFullScreen)

        label = ImageLabel()
        label.setPixmap(pixmap.scaled(self.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
        label.setAlignment(Qt.AlignCenter)

        layout = QVBoxLayout()
        layout.addWidget(label)
        self.setLayout(layout)

    def mousePressEvent(self, a0):
        self.close()

    def keyPressEvent(self, event):
        if event.key() == Qt.Key_Escape:
            self.close()

class LoginWindow(MessageBoxBase):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.title = SubtitleLabel('请验证', self)
        # self.username_label = BodyLabel("账号", self)
        # self.username_edit = LineEdit(self)
        # self.layout1 = QHBoxLayout()
        # self.layout1.addWidget(self.username_label, 0)
        # self.layout1.addWidget(self.username_edit, 1)

        self.password_label = BodyLabel("密码", self)
        self.password_edit = PasswordLineEdit (self)
        self.layout2 = QHBoxLayout()
        self.layout2.addWidget(self.password_label, 0)
        self.layout2.addWidget(self.password_edit, 1)

        self.viewLayout.addWidget(self.title)
        # self.viewLayout.addLayout(self.layout1)
        self.viewLayout.addLayout(self.layout2)

        self.yesButton.setText("确定")
        self.cancelButton.setText("取消")

        self.widget.setMinimumWidth(350)


class ModbusClientThread(QThread):
    trigger_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.is_checking = False
        self.modbus_client = None
        self._running = True
        self.loop = None
        self.reply_list = []


    def run(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.loop.run_until_complete(self.create_modbus())


    async def _write_register(self, address, value):
        try:
            if self.modbus_client:
                await self.modbus_client.write_register(address, value)
        except Exception as e:
            print(f"写寄存器失败：{e}")


    def write(self, address, value):
        if self.loop and self.modbus_client:
            asyncio.run_coroutine_threadsafe(
                self._write_register(address, value), self.loop
            )

    async def create_modbus(self):
        self.modbus_client = AsyncModbusTcpClient(host='************', port=502)
        await self.modbus_client.connect()

        while self._running:
            try:
                resp = await self.modbus_client.read_holding_registers(address=2000, count=1)
                if not resp.isError():
                    if resp.registers[0] == 1 and not self.is_checking:
                        self.is_checking = True
                        await asyncio.sleep(0.2)
                        self.trigger_signal.emit()

                resp_reply = await self.modbus_client.read_holding_registers(address=2001, count=1)
                if not resp_reply.isError():
                    if resp_reply.registers[0] == 1 and len(self.reply_list) > 0:
                        value = self.reply_list.pop(0)
                        self.write(address=2001, value=value)
                await asyncio.sleep(0.05)
            except Exception as e:
                print(f"Modbus read error: {e}")
                await asyncio.sleep(1)

    def stop(self):
        self._running = False
        if self.loop.is_running():
            self.modbus_client.close()
            self.loop.call_soon_threadsafe(self.loop.stop)


class Window(FluentWindow):
    def __init__(self):
        super().__init__()
        self.checked_pixmap = None
        self.source_pixmap = None
        self.init_window()
        self.device_manager = gx.DeviceManager()
        self.cam = None
        self.remote_device_feature = None
        self.image_process_config = None
        self.image_format_convert = None
        self.image_process = None

        self.init_camera()

        self.init_interface()
        self.setup_connections()

        self.model = None
        self.model_map = {
            "0": "blue",
            "1": "orange",
            "2": "white"
        }

        self.init_ui()
        self.modbus_thread = None
        self.start_modbus_thread()

    def start_modbus_thread(self):
        if not self.modbus_thread:
            self.modbus_thread = ModbusClientThread()
            self.modbus_thread.trigger_signal.connect(self.send_software_trigger)
            self.modbus_thread.start()

    def init_camera(self):
        try:
            dev_num, dev_info_list = self.device_manager.update_all_device_list()
            if dev_num == 0:
                return False

            sn = dev_info_list[0].get("sn")
            self.cam = self.device_manager.open_device_by_sn(sn)

            self.remote_device_feature = self.cam.get_remote_device_feature_control()
            self.image_process_config = self.cam.create_image_process_config()
            self.image_format_convert = self.device_manager.create_image_format_convert()
            self.image_process = self.device_manager.create_image_process()

            self.set_trigger_mode("On")
            self.set_trigger_source("Software")

            self.cam.data_stream[0].register_capture_callback(capture_callback)
            self.cam.stream_on()

        except Exception as e:
            print("Camera init failed:", e)

    def close_camera(self):
        if self.cam:
            self.cam.stream_off()
            self.cam.close_device()

    def set_trigger_mode(self, mode_str="On"):
        if self.remote_device_feature.is_implemented("TriggerMode"):
            self.remote_device_feature.get_enum_feature("TriggerMode").set(mode_str)

    def set_trigger_source(self, source_str="Software"):
        if self.remote_device_feature.is_implemented("TriggerSource"):
            self.remote_device_feature.get_enum_feature("TriggerSource").set(source_str)

    def send_software_trigger(self):
        if self.remote_device_feature:
            self.remote_device_feature.get_command_feature("TriggerSoftware").send_command()

        model_name = self.model_combo.currentText()
        logger.info(f"{model_name}正在检测")

    def set_exposure(self, value):
        if self.remote_device_feature:
            self.remote_device_feature.get_float_feature("ExposureTime").set(value)

    def set_gain(self, value):
        if self.remote_device_feature:
            self.remote_device_feature.get_float_feature("Gain").set(value)


    def init_interface(self):
        self.home_interface = QFrame(self)
        self.home_interface.setObjectName("home_interface")
        self.home_layout = QVBoxLayout(self.home_interface)
        self.home_top_layout = QHBoxLayout()
        self.home_bottom_layout = QHBoxLayout()

        self.home_card = GroupHeaderCardWidget(self.home_interface)
        self.home_card.setTitle("基本设置")
        self.home_card.setBorderRadius(8)

        self.model_layout = QHBoxLayout()
        self.model_label = BodyLabel("模型", self.home_card)
        self.model_combo = ComboBox(self.home_card)
        self.model_combo.addItems(["蓝色", "橙色", "白色"])
        self.model_combo.setFixedWidth(320)
        self.model_layout.setSpacing(10)
        self.model_layout.setContentsMargins(24, 15, 24, 20)
        self.model_layout.addWidget(self.model_label, 0, Qt.AlignLeft)
        self.model_layout.addStretch(1)
        self.model_layout.addWidget(self.model_combo, 0, Qt.AlignRight)
        self.model_layout.setAlignment(Qt.AlignVCenter)

        self.operation_layout = QHBoxLayout()
        self.operation_label = BodyLabel("操作", self.home_card)
        self.take_img_btn = PrimaryPushButton("手动检测", self.home_card)
        # self.save_img_btn = PushButton("保存图片", self.home_card)
        self.operation_layout.setSpacing(10)
        self.operation_layout.setContentsMargins(24, 15, 24, 20)
        self.operation_layout.addWidget(self.operation_label, 0, Qt.AlignLeft)
        self.operation_layout.addStretch(1)
        self.operation_layout.addWidget(self.take_img_btn, 0, Qt.AlignRight)
        # self.operation_layout.addWidget(self.save_img_btn, 0, Qt.AlignRight)
        self.operation_layout.setAlignment(Qt.AlignVCenter)

        self.home_card.vBoxLayout.addLayout(self.model_layout)
        self.home_card.vBoxLayout.addLayout(self.operation_layout)

        self.source_layout = QVBoxLayout()
        self.source_title = SubtitleLabel("原图", self.home_interface)
        self.source_image = ImageLabel(self.home_interface)
        self.source_layout.addWidget(self.source_title, 1)
        self.source_layout.addWidget(self.source_image, 19)

        self.check_layout = QVBoxLayout()
        self.check_title = SubtitleLabel("检测结果", self.home_interface)
        self.checked_image = ImageLabel(self.home_interface)
        self.check_layout.addWidget(self.check_title, 1)
        self.check_layout.addWidget(self.checked_image, 19)

        self.home_top_layout.addWidget(self.home_card)
        self.home_bottom_layout.addLayout(self.source_layout)
        self.home_bottom_layout.addLayout(self.check_layout)
        self.home_layout.addLayout(self.home_top_layout, 1)
        self.home_layout.addLayout(self.home_bottom_layout, 9)

        self.setting_interface = QFrame(self)
        self.setting_interface.setObjectName("setting_interface")
        self.settings_layout = QVBoxLayout(self.setting_interface)

        self.check_card = GroupHeaderCardWidget(self.setting_interface)
        self.check_card.setTitle("检测设置")
        self.check_card.setBorderRadius(8)

        self.hd_layout = QHBoxLayout()
        self.hd_label = BodyLabel("黑点检测", self.check_card)
        self.hd_combo = ComboBox(self.check_card)
        self.hd_combo.addItems(["是", "否"])
        self.hd_combo.setFixedWidth(320)
        self.hd_layout.setSpacing(10)
        self.hd_layout.setContentsMargins(24, 15, 24, 20)
        self.hd_layout.addWidget(self.hd_label, 0, Qt.AlignLeft)
        self.hd_layout.addStretch(1)
        self.hd_layout.addWidget(self.hd_combo, 0, Qt.AlignRight)
        self.hd_layout.setAlignment(Qt.AlignVCenter)

        self.ak_layout = QHBoxLayout()
        self.ak_label = BodyLabel("凹坑检测", self.check_card)
        self.ak_combo = ComboBox(self.check_card)
        self.ak_combo.addItems(["是", "否"])
        self.ak_combo.setFixedWidth(320)
        self.ak_layout.setSpacing(10)
        self.ak_layout.setContentsMargins(24, 15, 24, 20)
        self.ak_layout.addWidget(self.ak_label, 0, Qt.AlignLeft)
        self.ak_layout.addStretch(1)
        self.ak_layout.addWidget(self.ak_combo, 0, Qt.AlignRight)
        self.ak_layout.setAlignment(Qt.AlignVCenter)

        self.hh_layout = QHBoxLayout()
        self.hh_label = BodyLabel("划痕检测", self.check_card)
        self.hh_combo = ComboBox(self.check_card)
        self.hh_combo.addItems(["是", "否"])
        self.hh_combo.setFixedWidth(320)
        self.hh_layout.setSpacing(10)
        self.hh_layout.setContentsMargins(24, 15, 24, 20)
        self.hh_layout.addWidget(self.hh_label, 0, Qt.AlignLeft)
        self.hh_layout.addStretch(1)
        self.hh_layout.addWidget(self.hh_combo, 0, Qt.AlignRight)
        self.hh_layout.setAlignment(Qt.AlignVCenter)

        self.check_card.vBoxLayout.addLayout(self.hd_layout)
        self.check_card.vBoxLayout.addLayout(self.ak_layout)
        self.check_card.vBoxLayout.addLayout(self.hh_layout)

        self.camera_card = GroupHeaderCardWidget(self.setting_interface)
        self.camera_card.setTitle("相机设置")
        self.camera_card.setBorderRadius(8)

        self.exposure_layout = QHBoxLayout()
        self.exposure_label = BodyLabel("曝光", self.camera_card)
        self.exposure_input = DoubleSpinBox(self.camera_card)
        self.exposure_input.setMaximum(1000000000000.0)
        self.exposure_layout.setSpacing(10)
        self.exposure_layout.setContentsMargins(24, 15, 24, 20)
        self.exposure_layout.addWidget(self.exposure_label, 0, Qt.AlignLeft)
        self.exposure_layout.addStretch(1)
        self.exposure_layout.addWidget(self.exposure_input, 0, Qt.AlignRight)
        self.exposure_layout.setAlignment(Qt.AlignVCenter)

        self.gain_layout = QHBoxLayout()
        self.gain_label = BodyLabel("增益", self.camera_card)
        self.gain_input = DoubleSpinBox(self.camera_card)
        self.gain_layout.setSpacing(10)
        self.gain_layout.setContentsMargins(24, 15, 24, 20)
        self.gain_layout.addWidget(self.gain_label, 0, Qt.AlignLeft)
        self.gain_layout.addStretch(1)
        self.gain_layout.addWidget(self.gain_input, 0, Qt.AlignRight)
        self.gain_layout.setAlignment(Qt.AlignVCenter)

        self.camera_card.vBoxLayout.addLayout(self.exposure_layout)
        self.camera_card.vBoxLayout.addLayout(self.gain_layout)

        self.save_btn = PrimaryPushButton("保存", self.setting_interface)

        self.settings_layout.addWidget(self.check_card, 2)
        self.settings_layout.addWidget(self.camera_card, 2)
        self.settings_layout.addStretch(6)
        self.settings_layout.addWidget(self.save_btn)

        self.addSubInterface(self.home_interface, FIF.HOME, '检测')
        self.addSubInterface(self.setting_interface, FIF.SETTING, '设置')
        
        self.source_image.clicked.connect(lambda: self.show_full_image(self.source_pixmap))
        self.checked_image.clicked.connect(lambda: self.show_full_image(self.checked_pixmap))

    def init_window(self):
        self.resize(1024, 600)
        icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
        self.setWindowIcon(QIcon(icon_path))
        self.setWindowTitle('兆威8150视觉检测')

        self.showMaximized()

    def setup_connections(self):
        self.save_btn.clicked.connect(self.save_settings)

        self.take_img_btn.clicked.connect(self.send_software_trigger)
        # self.save_img_btn.clicked.connect(self.save_image)
        # self.model_combo.currentIndexChanged.connect(self.init_model)

    def save_settings(self):
        try:
            login_window = LoginWindow(self.setting_interface)
            if login_window.exec():
                if login_window.password_edit.text() != "123456":
                    InfoBar.error(
                        title='保存失败',
                        content='密码错误！',
                        orient=Qt.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP,
                        duration=2000,
                        parent=self.setting_interface
                    )
                    return

                hd_setting = self.hd_combo.currentText()
                ak_setting = self.ak_combo.currentText()
                hh_setting = self.hh_combo.currentText()

                exposure = self.exposure_input.value()
                gain = self.gain_input.value()

                self.set_gain(gain)
                self.set_exposure(exposure)

                logger.info(f"检测设置已更新 - 黑点检测: {hd_setting}, 凹坑检测: {ak_setting}, 划痕检测: {hh_setting}")
                logger.info(f"相机设置已更新 - 曝光: {exposure}, 增益: {gain}")

                InfoBar.success(
                    title='保存成功',
                    content='',
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=2000,
                    parent=self.setting_interface
                )
        except Exception as e:
            InfoBar.error(
                title='保存失败',
                content=str(e),
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.setting_interface
            )

    def init_ui(self):
        if self.remote_device_feature.is_implemented("ExposureTime") is True:
            exposure_value = self.remote_device_feature.get_float_feature("ExposureTime").get()
            self.exposure_input.setValue(exposure_value)

        if self.remote_device_feature.is_implemented("Gain") is True:
            gain_value = self.remote_device_feature.get_float_feature("Gain").get()
            self.gain_input.setValue(gain_value)

    def save_image(self):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        source_name = f"source_{timestamp}.bmp"
        # checked_name = f"checked_{timestamp}.bmp"
        cv2.imwrite(source_name, self.source_pixmap)
        # cv2.imwrite(checked_name, self.checked_pixmap)

        # logger.info(f"保存检测图片: {filename}")

    def handle_capture(self, raw_image):
        # 拍照后直接回复
        self.modbus_thread.write(2000, 3)
        pixel_format = raw_image.frame_data.pixel_format
        width = raw_image.frame_data.width
        height = raw_image.frame_data.height

        if pixel_format in [gx.GxPixelFormatEntry.MONO8]:
            buffer = (c_ubyte * (width * height))()
        else:
            buffer = (c_ubyte * (width * height * 3))()

        self.image_process_config.set_valid_bits(gx.DxValidBit.BIT0_7)
        self.image_process.image_improvement(raw_image, addressof(buffer), self.image_process_config)

        if gx.Utility.is_gray(raw_image.frame_data.pixel_format):
            rgb_image_array = (c_ubyte * raw_image.frame_data.height * raw_image.frame_data.width)()
            rgb_image_array_address = addressof(rgb_image_array)
        else:
            rgb_image_array = (c_ubyte * raw_image.frame_data.height * raw_image.frame_data.width * 3)()
            rgb_image_array_address = addressof(rgb_image_array)

        self.image_process.image_improvement(raw_image, rgb_image_array_address, self.image_process_config)

        numpy_img = np.frombuffer(rgb_image_array, dtype=np.ubyte,
                                       count=raw_image.frame_data.width * raw_image.frame_data.height * 3).reshape(
            raw_image.frame_data.height, raw_image.frame_data.width, 3)

        self.check_image(numpy_img)
        self.display_image(numpy_img, self.source_image)

    def check_image(self, img_np):
        try:
            img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
            _, img_encoded = cv2.imencode('.bmp', img_bgr)
            files = {'file': ('image.bmp', img_encoded.tobytes(), 'application/octet-stream')}
            data = {'description': self.model_map[str(self.model_combo.currentIndex())]}
            response = requests.post("http://**********:30304/image", files=files, data=data)
            _, result, data_list = response.text.split('--')


            result_np = resize_image_fixed(img_bgr)
            for item in ast.literal_eval(data_list):
                if item[0] == 'putText':
                    cv2.putText(result_np, item[1], item[2],
                                cv2.FONT_HERSHEY_SIMPLEX, item[3],
                                item[4], item[5], cv2.LINE_AA)
                elif item[0] == 'circle':
                    cv2.circle(result_np, item[1], item[2], item[3], item[4])
            self.display_image(cv2.cvtColor(result_np, cv2.COLOR_BGR2RGB), self.checked_image)

            if result == 'OK':
                input_value = 2
            else:
                input_value = 4
            self.modbus_thread.reply_list.append(input_value)
            self.modbus_thread.is_checking = False

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            source_name = f"source_{timestamp}.bmp"
            checked_name = f"checked_{timestamp}.bmp"

            cv2.imwrite(os.path.join("D://images", source_name), img_bgr)
            cv2.imwrite(os.path.join("D://images", checked_name),  result_np)


        except Exception as e:
            logger.error(f"检测错误：{e}")
            traceback.print_exc()

    def display_image(self, img_np, display: ImageLabel):
        if len(img_np.shape) == 2:
            qimg = QImage(img_np.data, img_np.shape[1], img_np.shape[0], img_np.shape[1], QImage.Format_Grayscale8)
        else:
            qimg = QImage(img_np.data, img_np.shape[1], img_np.shape[0], img_np.shape[1] * 3, QImage.Format_RGB888)

        pixmap = QPixmap.fromImage(qimg)
        if display == self.source_image:
            self.source_pixmap = pixmap
        else:
            self.checked_pixmap = pixmap

        display.setPixmap(pixmap.scaled(display.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
        display.setScaledSize(display.size())

    def show_full_image(self, image):
        if image is None:
            return

        viewer = FullscreenImageViewer(image)
        viewer.exec()

def capture_callback(raw_image):
    if main_window:
        main_window.handle_capture(raw_image)

def resize_image_fixed(image: np.ndarray) -> np.ndarray:
    scale = 0.2
    new_width = int(image.shape[1] * scale)
    new_height = int(image.shape[0] * scale)
    resized = cv2.resize(image, (new_width, new_height))
    return resized

if __name__ == '__main__':
    app = QApplication(sys.argv)
    main_window = Window()
    main_window.showMaximized()
    app.exec()
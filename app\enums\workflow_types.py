from enum import Enum


class WorkflowType(str, Enum):
    """工作流类型枚举"""

    SOP_TYPE1 = "sop_type1"  # 产线计划
    SOP_TYPE2 = "sop_type2"  # 控制计划

    @classmethod
    def get_all_types(cls):
        """获取所有工作流类型"""
        return [workflow_type.value for workflow_type in cls]

    @classmethod
    def get_sop_workflow_by_file_type(cls, file_type: int) -> str:
        """根据file_type获取对应的SOP工作流类型"""
        if file_type == 1:
            return cls.SOP_TYPE1
        elif file_type == 2:
            return cls.SOP_TYPE2
        else:
            raise ValueError(f"不支持的file_type: {file_type}，只支持1和2")

import io
import os

import pandas as pd


def excel_divider_stream(file_bytes: bytes, M: int = 40):
    # 读取表头和数据
    header_rows = pd.read_excel(io.BytesIO(file_bytes), header=None, nrows=7, dtype=str)
    df = pd.read_excel(io.BytesIO(file_bytes), header=6, dtype=str)

    df.columns = df.columns.str.strip().str.replace("\n", "")
    df["工序编号"] = df["工序编号"].ffill()

    group_sizes = df.groupby("工序编号").size().to_dict()

    # 分块逻辑
    chunks = []
    current_ops = []
    current_count = 0
    for op, rows in group_sizes.items():
        if current_count + rows > M:
            chunks.append((current_ops, current_count))
            current_ops = []
            current_count = 0
        current_ops.append(op)
        current_count += rows
    if current_ops:
        chunks.append((current_ops, current_count))

    # 生成每个分块的 Excel 文件流
    file_streams = []
    start_idx = 0
    for idx, (ops, total_rows) in enumerate(chunks, start=1):
        block_df = df.iloc[start_idx : start_idx + total_rows].copy()
        start_idx += total_rows

        stream = io.BytesIO()
        with pd.ExcelWriter(stream, engine="openpyxl") as writer:
            header_rows.to_excel(writer, index=False, header=False, sheet_name="Sheet1")
            block_df.to_excel(
                writer, index=False, header=False, startrow=7, sheet_name="Sheet1"
            )
        stream.seek(0)
        file_streams.append((f"chunk_{idx}.xlsx", stream))

    return file_streams


def cp_excel_divider_stream(file_bytes: bytes, M: int = 40):
    header_rows = pd.read_excel(
        io.BytesIO(file_bytes),
        header=None,
        nrows=10,
        dtype=str,
    )

    df = pd.read_excel(
        io.BytesIO(file_bytes),
        skiprows=10,
        header=None,
        dtype=str,
    )
    df.iloc[:, 0] = df.iloc[:, 0].ffill()  # 第一列为“工序编号”

    # 按工序编号分组并统计每组行数
    group_sizes = df.groupby(df.columns[0]).size().to_dict()

    current_group, current_size, splits = [], 0, []
    for process_no, size in group_sizes.items():
        if current_size + size > M and current_group:
            splits.append(current_group)
            current_group, current_size = [], 0
        current_group.append(process_no)
        current_size += size
    if current_group:
        splits.append(current_group)

    file_streams = []
    for idx, group in enumerate(splits, start=1):
        part_df = df[df[df.columns[0]].isin(group)]
        full_df = pd.concat([header_rows, part_df], ignore_index=True)

        stream = io.BytesIO()
        with pd.ExcelWriter(stream, engine="openpyxl") as writer:
            header_rows.to_excel(writer, index=False, header=False, sheet_name="Sheet1")
            full_df.to_excel(
                writer, index=False, header=False, startrow=10, sheet_name="Sheet1"
            )

        stream.seek(0)
        file_streams.append((f"chunk_{idx}.xlsx", stream))

        # output_path = os.path.join(f"chunk_{idx}.xlsx")
        # full_df.to_excel(output_path, index=False, header=False)

    return file_streams

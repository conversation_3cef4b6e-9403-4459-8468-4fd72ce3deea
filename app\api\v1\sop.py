from typing import Any, Dict

from fastapi import APIRouter, File, Form, UploadFile

from app.dependencies.common import get_sop_service_by_file_type

router = APIRouter()


@router.post("/upload", summary="上传SOP文件")
async def upload_sop_file(
    file: UploadFile = File(...),
    max_len: int = Form(40),
    file_type: int = Form(1),
) -> Dict[str, Any]:
    sop_service = get_sop_service_by_file_type(file_type)
    return await sop_service.process_upload(file, max_len, file_type)

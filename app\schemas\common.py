from typing import Any, Dict

from pydantic import BaseModel, Field


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""

    status: str = Field(description="服务状态")
    timestamp: float = Field(description="检查时间戳")
    version: str = Field(description="服务版本")
    environment: str = Field(description="运行环境")


class DifyWorkflowResult(BaseModel):
    """Dify工作流结果模型"""

    status: int
    data: Dict[str, Any] | None = None
    message: str | None = None


class FileUploadResult(BaseModel):
    """文件上传结果模型"""

    file_id: str
    filename: str
    size: int | None = None

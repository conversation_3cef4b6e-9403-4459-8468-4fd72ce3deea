stages:
  - build
  - deploy

build:
  stage: build
  tags:
    - shell
  script:
    - docker rmi ************:9092/ai_server:latest || true
    - docker build -t ************:9092/ai_server:latest .
    - docker push ************:9092/ai_server:latest
  only:
    - main

deploy:
  stage: deploy
  tags:
    - shell
  before_script:
    - eval "$(ssh-agent -s)"
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $SSH_HOST >> ~/.ssh/known_hosts
  script:
    - ssh -v $SSH_USER@$SSH_HOST
    - ssh $SSH_USER@$SSH_HOST "docker stop ai_server || true"
    - ssh $SSH_USER@$SSH_HOST "docker rm ai_server || true"
    - ssh $SSH_USER@$SSH_HOST "docker rmi ************:9092/ai_server:latest || true"
    - ssh $SSH_USER@$SSH_HOST "docker pull ************:9092/ai_server:latest"
    - ssh $SSH_USER@$SSH_HOST "docker run -d --name ai_server -p 30101:30101 -v /data/server/logs:/app/logs -e ENVIRONMENT=production -e DEBUG=false -e DIFY_SOP_TYPE1_API_KEY='$DIFY_SOP_TYPE1_API_KEY' -e DIFY_SOP_TYPE2_API_KEY='$DIFY_SOP_TYPE2_API_KEY' -e DIFY_API_URL='$DIFY_API_URL' -e DIFY_USER='$DIFY_USER' --restart unless-stopped ************:9092/ai_server:latest"
  environment:
    name: production
  only:
    - main